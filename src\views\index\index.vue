<template>
  <div class="index">

<van-overlay :show="personOverlay" @click="personOverlay = false">
  <div class="wrapper" @click.stop>
    <div style=" display: grid;place-items: center;margin-top: 50%;">
        <van-button style="width: 80%;" to="/logout" v-if="jugdeAPP()">退出登录</van-button>
      </div>
  </div>
</van-overlay>


    <div class="search-div">
      <div class="search-person" @click="personOverlay = true">
        <span><van-icon name="user-o" size="36"/></span>
        <span class="scan-text">{{ username }}</span>
      </div>
      <div class="search-input">
        <van-search v-model="keyword" placeholder="请输入设备名称、编号、科室" @search="onSearch"/>
      </div>

      <div class="search-scan" @click="onScan" v-if="jugdePlatform()">
        <span><van-icon name="scan" size="36"/></span>
        <span class="scan-text">扫一扫</span>
      </div>
    </div>
    <van-row gutter="20">
      <van-col span="8" v-for="button in statisticsButton" :key="button.id">
        <div class="statistics">
          <van-image width="38" height="38" :src="button.src"/>
        </div>
        <div class="statistics-text">
          {{ button.name }}
        </div>
      </van-col>
    </van-row>
    <div class="quick-menu">
      <van-grid :column-num="5" :gutter="10" :border="false" icon-size="2rem">
        <van-grid-item v-for="(value,index) in quickMenus" :key="index" :icon="iconPrefix +'/'+ value.icon" :text="value.actionname"
                       :badge="value.nums" @click="tourl(value.actionurl)"/>
      </van-grid>
    </div>
    <div style="background-color:#F8F8F8">
      <div class="index-menu" v-for="(item,index) in indexMenus" :key="index">
        <div class="menu-title">
          <span>{{ item.title }}</span>
          <span>更多</span>
        </div>
        <van-swipe class="my-swipe" :autoplay="0" :loop="false" indicator-color="#1989fa">
          <van-swipe-item v-for="(item1,index1) in item.swipe" :key="index1">
            <van-grid :border="false" icon-size="2.5rem">
              <van-grid-item :icon="iconPrefix +'/'+ item2.icon" :text="item2.actionname" v-for="(item2,index2) in item1.menu"
                             :key="index2" @click="tourl(item2.actionurl)"/>
            </van-grid>
          </van-swipe-item>
        </van-swipe>
      </div>
    </div>
    <!--    <van-button round block @click="testFeiShu">-->
    <!--      测试测试-->
    <!--    </van-button>-->
  </div>
</template>

<script>
import { Button, Icon, Col, Row, Image, Grid, GridItem, Search, Swipe, SwipeItem, Skeleton,Collapse, CollapseItem,Overlay  } from 'vant';

import { getMenus,getUserInfo } from "@/api/index/index";
import wechatUtil from '@/utils/wechatUtil';
import feishuUtil from '@/utils/feishuUtil';
import {getRegexAssnum} from "@/utils/regex";
export default {
  name: 'Index',
  data() {
    return {
      statisticsButton: [
        // {src: process.env.VUE_APP_BASE_PROXY_URL + '/Public/new-mobile/images/index/sbgk.png', name: '全院概况'},
        // {src: process.env.VUE_APP_BASE_PROXY_URL + '/Public/new-mobile/images/index/wxtj.png', name: '维修统计'},
        // {src: process.env.VUE_APP_BASE_PROXY_URL + '/Public/new-mobile/images/index/zysw.png', name: '重要事务'}
      ],
      quickMenus: [
        // {name: '审批',toUrl:'/M/Notin/approve', icon: process..env.VUE_APP_BASE_PROXY_URL + 'Public/new-mobile/images/index/action-icon/approve.png', num: 6},
        // {name: '验收', icon: process..env.VUE_APP_BASE_PROXY_URL + 'Public/new-mobile/images/index/action-icon/check.png', num: 8},
        // {name: '扫码报修', icon: process..env.VUE_APP_BASE_PROXY_URL + 'Public/new-mobile/images/index/action-icon/scan-addRepair.png'},
        // {name: '进程', icon: process..env.VUE_APP_BASE_PROXY_URL + 'Public/new-mobile/images/index/action-icon/progress.png', num: '99+'},
        // {name: '到期', icon: process..env.VUE_APP_BASE_PROXY_URL + 'Public/new-mobile/images/index/action-icon/expire.png'},
        // {name: '扫码检修', icon: process..env.VUE_APP_BASE_PROXY_URL + 'Public/new-mobile/images/index/action-icon/scan-acceptRepair.png'},
        // {name: '保养实施', icon: process..env.VUE_APP_BASE_PROXY_URL + 'Public/new-mobile/images/index/action-icon/patrol.png'},
        // {name: '公告',toUrl:'/M/Notice/getNoticeList', icon: process..env.VUE_APP_BASE_PROXY_URL + 'Public/new-mobile/images/index/action-icon/notice.png'},
        // {name: '应急预案',toUrl:'/M/Emergency/emergencyPlanList', icon: process..env.VUE_APP_BASE_PROXY_URL + 'Public/new-mobile/images/index/action-icon/emergency.png'},
        // {name: '更多', icon: process..env.VUE_APP_BASE_PROXY_URL + 'Public/new-mobile/images/index/action-icon/more.png'},
      ],
      indexMenus: [],
      keyword: '',
      skeletonShow: true,
      iconPrefix:process.env.VUE_APP_BASE_PROXY_URL+"/",
      personOverlay:false
    }
  },
  components: {
    [Button.name]: Button,
    [Icon.name]: Icon,
    [Col.name]: Col,
    [Row.name]: Row,
    [Image.name]: Image,
    [Grid.name]: Grid,
    [GridItem.name]: GridItem,
    [Search.name]: Search,
    [Swipe.name]: Swipe,
    [SwipeItem.name]: SwipeItem,
    [Skeleton.name]: Skeleton,
    [Collapse.name]: Collapse,
    [CollapseItem.name]: CollapseItem,
    [Overlay.name]: Overlay
  },
  computed:{
    username:function(){
      return localStorage.getItem('uname')
    }
  },
  mounted() {
    (() => {
      if (!this.$store.getters.menus[0]) {
        getMenus().then(response => {
          this.indexMenus = response.menu;
          this.quickMenus = response.kBtn;
          this.$store.commit('app/SET_MENUS', response.menu);
          this.$store.commit('app/SET_KBTN_MENUS', response.kBtn);
        })
      } else {
        this.indexMenus = this.$store.getters.menus;
        this.quickMenus = this.$store.getters.kBtn;
      }
  })(),
  this.getUserInfo(),
  this.$bus.$emit('changeLoginStatus',true)

  },
  methods: {
    jugdePlatform(){
      return (process.env.VUE_APP_PLATFORM == process.env.VUE_APP_WX)
    },
    jugdeAPP(){
      return (process.env.VUE_APP_PLATFORM == process.env.VUE_APP_APP || process.env.VUE_APP_PLATFORM == process.env.VUE_APP_WX)
    },
    getUserInfo(){
      if(process.env.VUE_APP_PLATFORM == process.env.VUE_APP_WX){
        if (localStorage.getItem('uname') == undefined) {
          getUserInfo().then(response => {
            localStorage.setItem('uname',response.username)
          })
        }
      }
    },
    // async testFeiShu() {
    // console.log(process.env.VUE_APP_VERSION)
    // await feishuUtil.init(['getLocation']);
    // window.h5sdk.ready(() => {
    //   // lark.ready参数为回调函数，在环境准备就绪时触发
    //   window.tt.getLocation({
    //     success(res) {
    //       alert(JSON.stringify(`经度 ${res.longitude}，纬度 ${res.latitude}`));
    //     },
    //     fail(res) {
    //       alert(JSON.stringify(res));
    //       alert(`getLocation 调用失败`);
    //     }
    //   });
    // });
    // },
    async tourl(url) {
      let _this = this;
      let fun_name = '';
      if (url.indexOf("addRepair") >= 0) {
        fun_name = 'addRepair';
      }
      if (url.indexOf("startup") >= 0) {
        fun_name = 'startup';
      }

      if (fun_name) {
        //扫码报修、开机管理
        switch (parseInt(process.env.VUE_APP_VERSION)) {
          case 1:
            // 微信版本
            wechatUtil.init([
              'scanQRCode',//扫一扫
            ]).then((wx) => {
              // 这里写微信的接口
              wx.scanQRCode({
                needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
                scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是一维码，默认二者都有
                async success(res) {
                  // var assnum = res.resultStr;// 当needResult 为 1 时，扫码返回的结果
                  // if (assnum.indexOf("ODE_") > 0) {
                  //   assnum = res.resultStr.substr(9);
                  // }
                  const assnum = await getRegexAssnum(res.resultStr)

                  _this.$router.push({
                    //name:'addRepair',
                    name: fun_name,
                    query: {
                      assnum: assnum
                    }
                  }).catch(err => {
                    if (err.name !== 'NavigationDuplicated') {
                        throw err
                    }
                  });
                },
                error: function (res) {
                  alert(res.errMsg);
                  if (res.errMsg.indexOf('function_not_exist') > 0) {
                    alert('版本过低请升级')
                  }
                }
              });
            });
            break;
          case 2:
            // 飞书版本
            await feishuUtil.init(['scanCode']);
            window.h5sdk.ready(() => {
              window.tt.scanCode({
                success(res) {
                  // alert(JSON.stringify(`${res.result}`));
                  _this.$router.push({
                    //name:'addRepair',
                    name: fun_name,
                    query: {
                      assnum: res.result
                    }
                  }).catch(err => {
                    if (err.name !== 'NavigationDuplicated') {
                        throw err
                    }
                  });
                },
                fail() {
                  alert(`调用失败`);
                }
              });
            });
            break;
        }
      } else {
        //直接路由跳转
        this.$router.push({
          path: url
        }).catch(err => {
          if (err.name !== 'NavigationDuplicated') {
              throw err
          }
        });
      }
    },
    onSearch(val) {
      if (val.replace(/^\s*|\s*$/g, "")) {
        this.$router.push({
          name: 'search',
          query: {
            keyword: val
          }
        }).catch(err => {
          if (err.name !== 'NavigationDuplicated') {
              throw err
          }
        });
      }
    },
    async onScan() {
      var _this = this;
      switch (parseInt(process.env.VUE_APP_VERSION)) {
        case 1:
          // 微信版本
          wechatUtil
              .init([
                'scanQRCode',//扫一扫
              ])
              .then((wx) => {
                // 这里写微信的接口
                wx.scanQRCode({
                  needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
                  scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是一维码，默认二者都有
                  async success(res) {
                    // var assnum = res.resultStr;// 当needResult 为 1 时，扫码返回的结果
                    const assnum = await getRegexAssnum(res.resultStr)

                    // if (assnum.indexOf("ODE_") > 0) {
                    //   assnum = res.resultStr.substr(9);
                    // }
                    _this.$router.push({
                      name: 'todo',
                      query: {
                        assnum: assnum
                      }
                    }).catch(err => {
                      if (err.name !== 'NavigationDuplicated') {
                          throw err
                      }
                    });
                  }
                });
              })
          break;
        case 2:
          // 飞书版本
          await feishuUtil.init(['scanCode']);
          window.h5sdk.ready(() => {
            window.tt.scanCode({
              success(res) {
                _this.$router.push({
                  name: 'todo',
                  query: {
                    assnum: res.result
                  }
                }).catch(err => {
                  if (err.name !== 'NavigationDuplicated') {
                      throw err
                  }
                });

              },
              fail() {
                alert(`调用失败`);
              }
            });
          });
          break;
      }
    }
  },
}
</script>

<style scoped lang="scss">
.index {
  height: 200px;
  background-size: cover;
  position: relative;
  background-image: url("../../assets/images/index/bg.png");
}

/*主页搜索条 S*/
.search-div {
  height: 60px;
  padding: 0.75rem;
}

.search-input {
  width: 75%;
  height: 36px;
  display: inline-block;
  margin-top: 0.3rem;
  margin-left: 0.5rem;
  .van-search {
    padding: 0;
    border-radius: 5px;
  }

  .van-search__content {
    background-color: #fff;
  }
}
.search-person {
  position: relative;
  float: left;
  width: 40px;
  span {
    color: #fff;
  }

  .van-icon {
    float: right;
  }

  .scan-text {
    display: block; width: 100%;
    text-align: center;
    position: absolute;
    top: 2rem;
    font-size: 12px;
    margin-top: 0.2rem;
  }
}
.search-scan {
  position: relative;
  float: right;

  span {
    color: #fff;
  }

  .van-icon {
    float: right;
  }

  .scan-text {
    position: absolute;
    right: 0;
    top: 2rem;
    font-size: 12px;
  }
}

/*主页搜索条 E*/
/*上方3个统计报表 S*/
.statistics {
  background-color: #fff;
  width: 48px;
  height: 48px;
  margin: 0 auto;
  text-align: center;
  border-radius: 5px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.5);

  .van-image {
    margin-top: 5px;
  }
}

.statistics-text {
  text-align: center;
  font-size: 14px;
  margin-top: 0.3rem;
}

/*上方3个统计报表 E*/
/*上方快速菜单 S*/
.quick-menu {
  background-color: #fff;
  margin: 10px;
  border-radius: 20px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3);
  height: 160px;
  padding-top: 5px;


}

/*上方快速菜单 E*/
.menu-title {
  margin: 20px 0 10px 0;
  padding: 10px 10px;

  span:first-child {
    font-weight: bold;
    font-size: 20px;
  }

  span:last-child {
    font-size: 14px;
    float: right;
    margin-top: 5px;
  }
}

.my-swipe {
  height: 200px;
}

.index-menu {
  background-color: #fff;
}
</style>
<style>
.van-grid-item__text {
  font-size: 14px;
  color: #000
}

.index .quick-menu .van-grid {
  margin-top: 20px;
}

.index .quick-menu .van-grid-item__content--center {
  padding: 0;
  background-color: inherit;
}

.index .index-menu .van-swipe__indicator {
  opacity: 1;
  background-color: #c8c9cc;
}
</style>
