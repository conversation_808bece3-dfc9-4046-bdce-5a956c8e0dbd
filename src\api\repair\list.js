import request from '@/utils/request'

export function getOrdersList(query) {
    return request({
        url: '/Repair/ordersLists',
        params: query
    })
}
export function getRepairList(query) {
    return request({
        url: '/Repair/getRepairLists',
        params: query
    })
}
export function partsOutWareList(query) {
    return request({
        url: '/RepairParts/partsOutWareList',
        params: query
    })
}
export function partStockList(query) {
    return request({
        url: '/RepairParts/partStockList',
        params: query
    })
}
export function examineList(query) {
    return request({
        url: '/Repair/examine',
        params: query
    })
}
export function getProgressList(query){
    return request({
        url: '/Repair/progress',
        params: query
    })
}
export function baoxiu(data){
    return request({
        url: '/Repair/addRepair',
        method: 'post',
        data
    })
}

// 获取个人维修记录
export function getPersonalRepairRecords(query) {
    return request({
        url: '/P/Repair/getRepairHistory',
        params: query
    })
}
