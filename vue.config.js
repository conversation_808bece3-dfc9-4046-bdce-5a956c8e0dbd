module.exports = {
    outputDir:'./dist', // 改输出目录地址方便构建
    publicPath: './',
    productionSourceMap: false,//去掉sourcemap 生产环境
    devServer: {
        port: 8082,
        disableHostCheck: true, // 添加这一行来解决 Invalid Host header 问题
        proxy: {                 //设置代理，必须填
            '/P': {              //设置拦截器  拦截器格式   斜杠+拦截器名字，名字可以自己定
                target: process.env.VUE_APP_BASE_PROXY_URL,     //代理的目标地址
                changeOrigin: true,              //是否设置同源，输入是的
                ws: true,                        //启用websockets
                pathRewrite: {                   //路径重写
                    // '/api':''                     //选择忽略拦截器里面的单词
                }
            }
        },
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
            'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization'
        }
    },
    lintOnSave: true, // 默认true  Eslint编译是否报错 设为false可临时强行编译 无视错误 调试用
    chainWebpack: config => {
        config
            .plugin('html')
            .tap(args => {
                args[0].title = '医疗设备管理系统';
                return args
            })
    }
};
