module.exports = {
    outputDir:'./dist', // 改输出目录地址方便构建
    publicPath: './',
    productionSourceMap: false,//去掉sourcemap 生产环境
    devServer: {
        port: 8082,
        proxy: {                 //设置代理，必须填
            '/P': {              //设置拦截器  拦截器格式   斜杠+拦截器名字，名字可以自己定
                target: process.env.VUE_APP_BASE_PROXY_URL,     //代理的目标地址
                changeOrigin: true,              //是否设置同源，输入是的
                headers: {
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                    'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization'
                }
            }
        }
    },
    lintOnSave: true, // 默认true  Eslint编译是否报错 设为false可临时强行编译 无视错误 调试用
    chainWebpack: config => {
        config
            .plugin('html')
            .tap(args => {
                args[0].title = '医疗设备管理系统';
                return args
            })
    }
};
