<template>
    <div v-if="page.is_display===1" class="con_detail">
        <div class="card-header">
            <h2 class="detailTitle">维修单详情</h2>
            <div class="bl1px"></div>
        </div>
        <RepairInfo :info='info'/>
        <van-tabs :border="false">
            <van-tab title="报价信息" v-if="info.company!=[]&&info.company!=undefined">
                <div class="card-header">
                    <h2 class="detailTitle">报价信息</h2>
                    <div class="bl1px"></div>
                </div>
                <van-cell-group v-for="item in info.company" :key="item.apprid">
                    <van-cell title="公司名称" :value="item.offer_company"/>
                    <van-cell title="公司资质">
                        <template #right-icon><span @click="img(item.pic_url)" v-html="item.aptitude"/></template>
                    </van-cell>
                    <van-cell title="联系人" :value="item.offer_contacts"/>
                    <van-cell title="联系人电话" :value="item.telphone"/>
                    <van-cell title="维修周期" :value="item.cycle"/>
                    <van-cell title="发票" :value="item.invoice"/>
                    <van-cell title="费用" :value="item.total_price"/>
                </van-cell-group>
            </van-tab>

            <van-tab title="配件信息" v-if="info.parts!=[]&&info.parts!=undefined&&info.parts.length>0">
                <div class="card-header">
                    <h2 class="detailTitle">配件信息</h2>
                    <div class="bl1px"></div>
                </div>
                <van-cell-group v-for="item in info.parts" :key="item.apprid">
                    <van-cell title="配件名称" :value="item.parts"/>
                    <van-cell title="规格型号" :value="item.part_model"/>
                    <van-cell title="数量" :value="item.part_num"/>
                </van-cell-group>
            </van-tab>

            <van-tab title="审批信息" v-if="info.approves.length > 0">
                <div class="card-header">
                    <h2 class="detailTitle">审批信息</h2>
                    <div class="bl1px"></div>
                </div>
                <Approves :info='info.approves'/>
            </van-tab>
        </van-tabs>
        <div class="card-header">
            <img class="formImage" :src="require('@/assets/images/form/form.png')">
            <h2 class="formTitle">设备维修进程</h2>
        </div>
        <van-steps direction="vertical" :active="0">
            <van-step v-for="(item,index) in info.life" :key="index">
                <h3>{{item.statusName}}{{item.date}}</h3>
                <p>{{item.user}}({{item.telephone}})<span v-html="item.text"></span></p>
            </van-step>
        </van-steps>
    </div>
    <div v-else>
        <div class="global-loading">
            <van-loading color="#1989fa" size="60px"/>
        </div>
    </div>
</template>

<script>
    import {Divider, Cell, CellGroup, Icon, Field, Form, Button, Tab, Tabs,Loading,Popup,Picker,DatetimePicker,Step, Steps,ImagePreview} from 'vant';
    import {getInfo} from "@/api/repair/progress";
    import RepairInfo from "@/components/RepairInfo";
    import Approves from '@/components/Approves';

    export default {
        name: 'check',
        components: {
            [Divider.name]: Divider,
            [Cell.name]: Cell,
            [CellGroup.name]: CellGroup,
            [Icon.name]: Icon,
            [Field.name]: Field,
            [Form.name]: Form,
            [Button.name]: Button,
            [Tab.name]: Tab,
            [Tabs.name]: Tabs,
            [Loading.name]: Loading,
            [Popup.name]: Popup,
            [Picker.name]: Picker,
            [Step.name]: Step,
            [ImagePreview.name]: ImagePreview,
            [Steps.name]: Steps,
            [DatetimePicker.name]: DatetimePicker,
            RepairInfo,
            Approves,
        },
        data() {
            return {
                info: {
                    company: [],
                    parts: [],
                    approves: [],
                },
                page: {
                    is_display: 0,//用于判断是否加载完数据
                },
                form: {},
            }
        },
        methods: {
            img(urlList){
                ImagePreview(urlList.map(url => process.env.VUE_APP_BASE_PROXY_URL + url));
            },
            getInfo(repid) {
                let params = {repid: repid};
                getInfo(params).then(response => {
                    this.info = response.repArr;
                    this.info.company = response.company;
                    this.info.parts = response.parts;
                    this.info.approves = response.approves;
                    this.info.life = response.life;
                    this.page.is_display = 1;

                })
            },
        }, mounted() {
            //获取相关设备信息
            this.getInfo(this.$route.query.repid);
        }
    }
</script>

<style scoped lang="scss">
    .van-button {
        margin-top: 10px;
    }
   ::v-deep .gray{
        color: #666;
        margin-left: 10px;
    }
   ::v-deep .red{color: #FF5722}
   .van-tabs--line .van-tabs__wrap {
        height: 0;
    }
</style>
