{"name": "tecev-mobile", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --open", "dev": "npm run serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"core-js": "^3.6.4", "jsencrypt": "^3.0.0-rc.1", "pnpm": "^8.11.0", "sha1": "^1.1.1", "uni-simple-router": "^1.5.5", "vant": "^2.8.7", "vconsole": "^3.15.1", "vue": "^2.6.11", "vue-drag-verify2": "^1.1.0", "vue-pdf": "^4.1.0", "vue-puzzle-vcode": "^1.1.4", "vue-router": "^3.1.6", "vuex": "^3.1.3", "weixin-js-sdk": "^1.6.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.3.0", "@vue/cli-plugin-eslint": "~4.3.0", "@vue/cli-plugin-router": "~4.3.0", "@vue/cli-plugin-vuex": "~4.3.0", "@vue/cli-service": "~4.3.0", "axios": "^0.19.2", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.13.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "mockjs": "^1.1.0", "sass": "^1.80.6", "sass-loader": "^10.0.1", "vue-template-compiler": "^2.6.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "apidoc": {"name": "天成医疗设备管理系统移动端接口文档", "version": "0.1.0", "description": "这是一个接口文档", "title": "接口文档", "header": {"title": "文档说明", "filename": "src/doc_file/header.md"}, "footer": {"title": "文档结尾", "filename": "src/doc_file/footer.md"}}}