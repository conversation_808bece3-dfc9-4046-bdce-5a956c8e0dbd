<template>
  <div class="con_detail">
    <div class="card-header">
      <h2 class="detailTitle">设备基本信息</h2>
      <div class="bl1px"></div>
    </div>
    <AssetsInfo :info='info'/>
    <div class="card-header">
      <img class="formImage" :src="require('@/assets/images/form/form.png')">
      <h2 class="formTitle">报修表单</h2>
    </div>
    <van-form ref="repairForm" @submit="onSubmit">
      <van-field v-if="this.issystem['repair_person'] != 1"
          v-model="form.repair_person"
          name="repair_person"
          label="报修人"
          placeholder="请填写报修人"
          required
          :rules="[{ required: true, message: '报修人不能为空' }]"
      />
      <van-field v-if="this.issystem['repair_phone'] != 1"
          v-model="form.repair_phone"
          name="repair_phone"
          label="联系电话"
          placeholder="请填写报修人联系电话"
          required
          :rules="[{ required: true, message: '电话不能为空' }]"
      />
      <div v-if="this.issystem['repair_date'] != 1">
        <van-field
            :value="form.repair_date"
            is-link
            readonly
            name="repair_date"
            label="报修时间"
            placeholder="点击选择报修时间"
            required
            :rules="[{ validator, message: '请选择报修时间',trigger:'onChange' }]"
            @click="showRepairDate = true"
        />
        <van-popup v-model="showRepairDate" position="bottom">
          <van-datetime-picker
              v-model="currentDate"
              type="datetime"
              title="选择报修时间"
              :max-date="maxDate"
              @confirm="onConfirmRepairDate"
              @cancel="showRepairDate = false"
          />
        </van-popup>
      </div>
      <van-field
          v-model="form.breakdown"
          name="breakdown"
          rows="3"
          type="textarea"
          maxlength="100"
          autosize
          label="故障描述"
          placeholder="故障描述(可不填)"
          show-word-limit
      />
      <div v-if="jugdePlatform()">
        <van-field v-model="form.wxTapeAmr" name="wxTapeAmr" label="语音描述">
          <template #input>
            <van-icon class-prefix="wx-icon" name="yuyin" size="25" @click="yuyin"/>
            <van-button plain hairline type="primary" native-type="button" size="mini" :icon="icon" v-show="is_show"
                        class="bf" @click="bofang">{{ voiceTime }}” 播放
            </van-button>
          </template>
        </van-field>
      </div>

      <van-field v-model="form.fileList" name="fileList" label="故障照片">
        <template #input>
          <van-uploader image-fit="cover" max-size="10485760" @oversize="oversize" :max-count="page.max_count"
                        :after-read="afterRead" :before-read="beforeRead" :before-delete="delImg"
                        v-model="page.fileList" multiple/>
        </template>
      </van-field>
      <div style="margin: 16px;">
        <van-button square block color="#009688" native-type="submit">
          确认并提交
        </van-button>
      </div>
      <canvas id="myCanvas" width="" height="" style="border:1px solid #d3d3d3;background:#ffffff;display:none;">
        Your browser does not support the HTML5 canvas tag.
      </canvas>
      <audio :src="form.wxTapeAmr" id="audio"></audio>
    </van-form>
  </div>
</template>

<script>
import Qs from 'qs';
import {
  Divider,
  Cell,
  CellGroup,
  Icon,
  Field,
  Toast,
  Form,
  Button,
  Tab,
  Tabs,
  Popup,
  Picker,
  DatetimePicker,
  Uploader,
  Dialog,
  Notify
} from 'vant';
import { getInfo, submit } from "@/api/repair/add";
import { downRecord, uploadImg } from "@/api/wechat";
import AssetsInfo from "@/components/Assetsinfo";
import wechatUtil from '@/utils/wechatUtil';
import feishuUtil from "@/utils/feishuUtil";
import { uploadFile } from "@/api/feishu";

export default {
  name: 'add',
  components: {
    [Divider.name]: Divider,
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [Icon.name]: Icon,
    [Field.name]: Field,
    [Form.name]: Form,
    [Button.name]: Button,
    [Tab.name]: Tab,
    [Tabs.name]: Tabs,
    [Popup.name]: Popup,
    [Uploader.name]: Uploader,
    [Picker.name]: Picker,
    [DatetimePicker.name]: DatetimePicker,
    [Dialog.Component.name]: Dialog.Component,
    AssetsInfo,
  },
  data() {
    return {
      info: {
        factoryInfo: [],
        supplierInfo: [],
        repairInfo: [],
        repairdata: [],
      },
      showRepairDate: false,
      issystem:[],
      currentDate: new Date(),
      maxDate: new Date(),
      page: {
        response: '',
        response_date: '',
        fileList: [],
        max_count: 2
      },
      form: {
        repair_date: '',
        repair_phone: '',
        repair_person: '',
        breakdown: '',
        assid: '',
        from: 1,
        assnum: '',
        assets: '',
        opendate: '',
        factory: '',
        model: '',
        factorynum: '',
        department: '',
        departid: '',
        assprice: '',
        wxTapeAmr: '',
        filename: ''
      },
      voiceTime: 0,
      is_show: false,
      icon: 'play-circle-o',
      imgMaxSize: 1 * 1024 * 1024, // 3M
      mediumquality: 0.7,
    }
  },
  methods: {
    jugdePlatform(){
      return (process.env.VUE_APP_PLATFORM == process.env.VUE_APP_WX)
    },
    async yuyin() {
      let _this = this;
      switch (parseInt(process.env.VUE_APP_VERSION)) {
        case 1:
          // 微信版本
          wechatUtil.init([
            'startRecord',//开始录音
            'stopRecord',//停止录音
            'onVoiceRecordEnd',//监听录音自动停止
            'playVoice',//播放语音
            'pauseVoice',//暂停播放
            'stopVoice',//停止播放
            'onVoicePlayEnd',//监听语音播放完毕
            'uploadVoice',//上传语音
          ]).then((wx) => {
            // 这里写微信的接口
            //开始录音
            wx.startRecord();
            //录音时间超过一分钟没有停止的时候会执行 complete 回调
            wx.onVoiceRecordEnd({
              complete: function (res) {
                let tmp_localId = res.localId;
                Dialog.close();//自动关闭弹出框
                //上传语音
                wx.uploadVoice({
                  localId: tmp_localId, // 需要上传的音频的本地ID，由stopRecord接口获得
                  isShowProgressTips: 1, // 默认为1，显示进度提示
                  success: function (res) {
                    Toast('上传成功，正在下载语音到本地');
                    let serverId = res.serverId; // 返回音频的服务器端ID
                    let params = {};
                    params.mid = serverId;
                    downRecord(Qs.stringify(params)).then(response => {
                      if (response.status === 1) {
                        _this.is_show = true;
                        _this.form.wxTapeAmr = response.info['record_url'];
                        _this.voiceTime = response.info['seconds'];
                      } else {
                        Toast.fail(response.msg);
                      }
                    });
                  }
                });
              }
            });
            Dialog.confirm({
              title: '正在录音...',
              allowHtml: true,//是否允许 message 内容中渲染 HTML
              cancelButtonText: '取消录音',
              //confirmButtonText:'确认并上传录音',
              message: '录音时长最长为一分钟，超时自动上传！',
            }).then(() => {
              // on confirm上传语音
              //停止录音
              wx.stopRecord({
                success: function (res) {
                  let localId = res.localId;
                  //上传语音
                  wx.uploadVoice({
                    localId: localId, // 需要上传的音频的本地ID，由stopRecord接口获得
                    isShowProgressTips: 1, // 默认为1，显示进度提示
                    success: function (res) {
                      Toast('上传成功，正在下载语音到本地');
                      let serverId = res.serverId; // 返回音频的服务器端ID
                      //下载语音到服务器
                      let params = {};
                      params.mid = serverId;
                      downRecord(Qs.stringify(params)).then(response => {
                        if (response.status === 1) {
                          _this.is_show = true;
                          _this.form.wxTapeAmr = response.info['record_url'];
                          _this.voiceTime = response.info['seconds'];
                        } else {
                          Toast.fail(response.msg);
                        }
                      });

                    }
                  });
                }
              });

            }).catch(() => {
              // on cancel
              //停止录音
              wx.stopRecord({
                success: function (/*res*/) {
                  //var localId = res.localId;
                }
              });
            });
          });
          break;
        case 2:
          // 飞书版本
          await feishuUtil.init(['getRecorderManager', 'saveFile']);
          window.h5sdk.ready(() => {
            const recorderManager = window.tt.getRecorderManager();
            // 是否上传 isUpload
            let isUpload = false;
            //开始录音
            recorderManager.start();
            // 弹窗显示录音
            Dialog.confirm({
              title: '正在录音...',
              allowHtml: true,//是否允许 message 内容中渲染 HTML
              cancelButtonText: '取消录音',
              //confirmButtonText:'确认并上传录音',
              message: '录音时长最长为一分钟，超时自动上传！',
            }).then(() => {
              //停止录音
              recorderManager.stop();
              // 标注为上传录音
              isUpload = true;
            }).catch(() => {
              //停止录音
              recorderManager.stop();
            });
            // 监听停止
            recorderManager.onStop((res) => {
              if (isUpload) {
                // alert(JSON.stringify(res))
                const {tempFilePath} = res;
                alert('临时地址为：' + tempFilePath)
                alert('飞书版录音功能暂未开发');
                // if (tempFilePath) {
                //   window.tt.saveFile({
                //     tempFilePath,
                //     success(res1) {
                //       alert(`文件地址是：${res1.savedFilePath}`);
                //       alert('飞书版录音功能暂未开发');
                //       // uploadFile(res1.savedFilePath).then(response => {
                //       //   alert(JSON.stringify(response))
                //       // })
                //       // _this.is_show = true;
                //       // _this.form.wxTapeAmr = res1.savedFilePath;
                //     },
                //     fail(res1) {
                //       alert(`saveFile 调用失败` + JSON.stringify(res1));
                //     }
                //   });
                // }
              }
            })
          });
          break;
      }
    },
    bofang() {
      let audio = document.getElementById("audio");
      if (audio.paused) { //判断当前的状态是否为暂停，若是则点击播放，否则暂停
        this.icon = 'pause-circle-o';
        audio.play();
        let sec = this.voiceTime * 1000;
        //变更图标
        setTimeout(() => {
          this.icon = 'play-circle-o';
        }, sec);
      } else {
        this.icon = 'play-circle-o';
        audio.pause();
      }
    },
    oversize() {
      //超出限制大小
      Notify({type: 'danger', message: '图片超出10M大小限制'});
      return false;
    },
    beforeRead(file) {
      // if (file.type !== 'image/jpeg' && file.type !== 'image/png') {
      //     Notify({ type: 'danger', message: '请上传jpg/jpeg/png文件' });
      //     return false;
      // }
      if (file.length > this.page.max_count) {
        Notify({type: 'danger', message: `最多只能选择${this.page.max_count}个图片```});
        return false
      }
      return true;
    },
    afterRead(file) {
      file.status = 'uploading';
      file.message = '上传中...';
      //上传图片
      let params = {};
      params.action = 'upload';
      params.base64 = file.content;
      uploadImg(Qs.stringify(params)).then(res => {
        if (res.status === 1) {
          //上传成功
          file.status = 'success';
          file.message = '上传成功';
          file.src = res.data['src'];
          this.form.filename += res.data['src'] + ',';
        } else {
          file.status = 'failed';
          file.message = '上传失败';
        }
      });
    },
    delImg(file) {
      let str = file.src + ',';
      this.form.filename = this.form.filename.replace(str, "");
      return true;
    },
    onSubmit(/*values*/) {
      this.form.from = 0;
      let _this = this;
      submit(Qs.stringify(this.form)).then(response => {
        if (response.status === 1) {
          Toast({
            type: 'success',//失败fail
            duration: 2000,//2秒
            message: response.msg,
            icon: 'success',//失败cross
            forbidClick: true,//是否禁止背景点击，避免重复提交
            onClose() {
              //关闭后跳转到首页
              _this.$router.push('/');
            }
          });
        } else {
          Toast.fail(response.msg);
        }
      });
    },
    getInfo(assnum) {
      let params = {assnum: assnum};
      getInfo(params).then(response => {
        this.info = response.asArr;
        this.form.assid = response.asArr.assid;
        this.form.assnum = response.asArr.assnum;
        this.form.assets = response.asArr.assets;
        this.form.opendate = response.asArr.opendate;
        this.form.factory = response.asArr.factory;
        this.form.model = response.asArr.model;
        this.form.factorynum = response.asArr.factorynum;
        this.form.department = response.asArr.department;
        this.form.departid = response.asArr.departid;
        this.form.assprice = response.asArr.buy_price;
        if (response.hasOwnProperty('repair_person')) {
          this.form.repair_person = response.repair_person;
        }
        if (response.hasOwnProperty('repair_phone')) {
          this.form.repair_phone = response.repair_phone;
        }
        if (response.hasOwnProperty('repair_date')) {
          this.form.repair_date = response.repair_date;
        }
        this.issystem = response.issystem;
      })
    },
    validator(val){
      if (this.showRepairDate) {
        return true;
      }else if (val) {
        return true;
      }
      return false;
    },
    //选择报修时间
    onConfirmRepairDate(time) {
      this.form.repair_date = this.timeFormat(time);
      this.showRepairDate = false;
    },
    timeFormat(time) { // 时间格式化
      let year = time.getFullYear();
      let month = time.getMonth() + 1;
      let day = time.getDate();
      let h = time.getHours();
      let m = time.getMinutes();
      month = month < 10 ? '0'+month : month;
      day = day < 10 ? '0'+day : day;
      h = h < 10 ? '0'+h : h;
      m = m < 10 ? '0'+m : m;
      return year + '-' + month + '-' + day + ' ' + h + ':' + m;
    },
  }, mounted() {
    //获取相关设备信息
    this.getInfo(this.$route.query.assnum);
  }
}
</script>
<style scoped lang="scss">
.bf {
  padding: 0 5px;
  margin: 0 auto;
}

.van-uploader {
  margin-left: 0;
}
</style>
