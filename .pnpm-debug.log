{"0 debug pnpm:scope": {"selected": 1}, "1 error pnpm": {"errno": 1, "code": "ELIFECYCLE", "pkgid": "tecev-mobile@0.1.0", "stage": "serve", "script": "vue-cli-service serve --open", "pkgname": "tecev-mobile", "err": {"name": "Error", "message": "tecev-mobile@0.1.0 serve: `vue-cli-service serve --open`\nExit status 1", "code": "ELIFECYCLE", "stack": "Error: tecev-mobile@0.1.0 serve: `vue-cli-service serve --open`\nExit status 1\n    at EventEmitter.<anonymous> (C:\\Users\\<USER>\\.node\\corepack\\pnpm\\6.11.0\\dist\\pnpm.cjs:104774:20)\n    at EventEmitter.emit (node:events:526:28)\n    at ChildProcess.<anonymous> (C:\\Users\\<USER>\\.node\\corepack\\pnpm\\6.11.0\\dist\\pnpm.cjs:93080:18)\n    at ChildProcess.emit (node:events:526:28)\n    at maybeClose (node:internal/child_process:1092:16)\n    at Process.ChildProcess._handle.onexit (node:internal/child_process:302:5)"}}}